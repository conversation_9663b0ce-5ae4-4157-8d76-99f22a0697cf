import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const { t } = useLanguage();
  const { user } = useAuth();

  const features = [
    {
      title: t('fastDelivery'),
      description: t('fastDeliveryDescription'),
      icon: "Clock"
    },
    {
      title: t('realtimeTracking'),
      description: t('realtimeTrackingDescription'),
      icon: "MapPin"
    },
    {
      title: t('professionalDrivers'),
      description: t('professionalDriversDescription'),
      icon: "Truck"
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl font-extrabold text-gray-900 mb-6">
            {t('heroTitle')}
          </h1>
          <p className="text-lg text-gray-700 mb-8">
            {t('heroSubtitle')}
          </p>
          <div className="flex justify-center space-x-4">
            <Link to="/send">
              <Button className="bg-blue-600 hover:bg-blue-700">
                {t('sendPackage')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/track">
              <Button variant="outline">
                {t('trackPackage')}
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
            {t('testimonials')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <p className="text-gray-700 italic mb-4">
                {t('testimonial1')}
              </p>
              <div className="flex items-center">
                <div className="ml-3">
                  <div className="text-gray-900 font-semibold">{t('customer1')}</div>
                  <div className="text-gray-600">{t('location1')}</div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <p className="text-gray-700 italic mb-4">
                {t('testimonial2')}
              </p>
              <div className="flex items-center">
                <div className="ml-3">
                  <div className="text-gray-900 font-semibold">{t('customer2')}</div>
                  <div className="text-gray-600">{t('location2')}</div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <p className="text-gray-700 italic mb-4">
                {t('testimonial3')}
              </p>
              <div className="flex items-center">
                <div className="ml-3">
                  <div className="text-gray-900 font-semibold">{t('customer3')}</div>
                  <div className="text-gray-600">{t('location3')}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 bg-blue-100">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-blue-900 mb-6">
            {t('readyToSend')}
          </h2>
          <p className="text-lg text-blue-700 mb-8">
            {t('startSendingToday')}
          </p>
          <Link to="/send">
            <Button className="bg-orange-600 hover:bg-orange-700">
              {t('getStarted')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Index;
