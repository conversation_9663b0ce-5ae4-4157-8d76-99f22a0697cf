import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Search, Package, MapPin, Clock, CheckCircle, Truck, ArrowLeft, Phone } from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from "sonner";

const TrackPackage = () => {
  const [trackingId, setTrackingId] = useState("");
  const [trackingData, setTrackingData] = useState<any>(null);

  // Mock tracking data
  const mockTrackingData = {
    id: "**********",
    status: "in_transit",
    progress: 75,
    pickup: {
      address: "123 Main St, Downtown",
      time: "2:30 PM",
      completed: true
    },
    delivery: {
      address: "456 Oak Ave, Uptown",
      estimatedTime: "4:15 PM",
      completed: false
    },
    driver: {
      name: "<PERSON>",
      phone: "+****************",
      vehicle: "Honda Civic - ABC 123"
    },
    timeline: [
      {
        status: "Package picked up",
        time: "2:30 PM",
        completed: true,
        icon: Package
      },
      {
        status: "In transit",
        time: "3:45 PM",
        completed: true,
        icon: Truck
      },
      {
        status: "Out for delivery",
        time: "4:00 PM",
        completed: false,
        icon: MapPin
      },
      {
        status: "Delivered",
        time: "Expected 4:15 PM",
        completed: false,
        icon: CheckCircle
      }
    ]
  };

  const handleTrackPackage = () => {
    if (!trackingId.trim()) {
      toast.error("Please enter a tracking ID");
      return;
    }
    
    setTrackingData(mockTrackingData);
    toast.success("Package found!");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "picked_up": return "text-blue-600";
      case "in_transit": return "text-orange-600";
      case "out_for_delivery": return "text-purple-600";
      case "delivered": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "picked_up": return "Picked Up";
      case "in_transit": return "In Transit";
      case "out_for_delivery": return "Out for Delivery";
      case "delivered": return "Delivered";
      default: return "Unknown";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      {/* Remove the existing header section since we have global Header */}

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <Search className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Track Your Package</h1>
            <p className="text-gray-600">Enter your tracking ID to see real-time updates</p>
          </div>

          {/* Tracking Input */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Package Tracking</CardTitle>
              <CardDescription>
                Enter your tracking ID to get real-time delivery updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Label htmlFor="tracking" className="sr-only">Tracking ID</Label>
                  <Input
                    id="tracking"
                    placeholder="Enter tracking ID (e.g., **********)"
                    value={trackingId}
                    onChange={(e) => setTrackingId(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleTrackPackage()}
                  />
                </div>
                <Button onClick={handleTrackPackage} className="bg-blue-600 hover:bg-blue-700">
                  <Search className="h-4 w-4 mr-2" />
                  Track
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tracking Results */}
          {trackingData && (
            <div className="space-y-6">
              {/* Status Overview */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <Package className="h-5 w-5" />
                        <span>Package {trackingData.id}</span>
                      </CardTitle>
                      <CardDescription className={getStatusColor(trackingData.status)}>
                        {getStatusText(trackingData.status)}
                      </CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">{trackingData.progress}%</div>
                      <div className="text-sm text-gray-500">Complete</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Progress value={trackingData.progress} className="mb-4" />
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-green-600" />
                        <span className="font-medium">Pickup</span>
                      </div>
                      <p className="text-gray-600">{trackingData.pickup.address}</p>
                      <p className="text-sm text-green-600">✓ Completed at {trackingData.pickup.time}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">Delivery</span>
                      </div>
                      <p className="text-gray-600">{trackingData.delivery.address}</p>
                      <p className="text-sm text-orange-600">ETA: {trackingData.delivery.estimatedTime}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid lg:grid-cols-3 gap-6">
                {/* Timeline */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Clock className="h-5 w-5" />
                        <span>Delivery Timeline</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {trackingData.timeline.map((item: any, index: number) => {
                          const Icon = item.icon;
                          return (
                            <div key={index} className="flex items-center space-x-4">
                              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                item.completed ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                              }`}>
                                <Icon className="h-5 w-5" />
                              </div>
                              <div className="flex-1">
                                <h3 className={`font-medium ${item.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                                  {item.status}
                                </h3>
                                <p className="text-sm text-gray-500">{item.time}</p>
                              </div>
                              {item.completed && (
                                <CheckCircle className="h-5 w-5 text-green-600" />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Driver Info & Map */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Truck className="h-5 w-5" />
                        <span>Your Driver</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                          <span className="text-xl font-bold text-blue-600">
                            {trackingData.driver.name.split(' ').map((n: string) => n[0]).join('')}
                          </span>
                        </div>
                        <h3 className="font-semibold">{trackingData.driver.name}</h3>
                        <p className="text-sm text-gray-600">{trackingData.driver.vehicle}</p>
                      </div>
                      <Button variant="outline" className="w-full">
                        <Phone className="h-4 w-4 mr-2" />
                        Call Driver
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Live Map</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-gray-100 rounded-lg p-8 text-center">
                        <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">Interactive map would appear here</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          )}

          {/* No tracking data state */}
          {!trackingData && (
            <Card className="text-center py-12">
              <CardContent>
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">No package data</h3>
                <p className="text-gray-500">Enter a tracking ID above to see package details</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrackPackage;
