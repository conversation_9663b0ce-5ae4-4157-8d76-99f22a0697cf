export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      drivers: {
        Row: {
          created_at: string | null
          current_lat: number | null
          current_lng: number | null
          earnings_today: number | null
          id: string
          license_number: string
          rating: number | null
          status: Database["public"]["Enums"]["driver_status"] | null
          total_deliveries: number | null
          updated_at: string | null
          vehicle_number: string
          vehicle_type: string
        }
        Insert: {
          created_at?: string | null
          current_lat?: number | null
          current_lng?: number | null
          earnings_today?: number | null
          id: string
          license_number: string
          rating?: number | null
          status?: Database["public"]["Enums"]["driver_status"] | null
          total_deliveries?: number | null
          updated_at?: string | null
          vehicle_number: string
          vehicle_type: string
        }
        Update: {
          created_at?: string | null
          current_lat?: number | null
          current_lng?: number | null
          earnings_today?: number | null
          id?: string
          license_number?: string
          rating?: number | null
          status?: Database["public"]["Enums"]["driver_status"] | null
          total_deliveries?: number | null
          updated_at?: string | null
          vehicle_number?: string
          vehicle_type?: string
        }
        Relationships: []
      }
      order_tracking: {
        Row: {
          driver_lat: number
          driver_lng: number
          id: string
          order_id: string
          timestamp: string | null
        }
        Insert: {
          driver_lat: number
          driver_lng: number
          id?: string
          order_id: string
          timestamp?: string | null
        }
        Update: {
          driver_lat?: number
          driver_lng?: number
          id?: string
          order_id?: string
          timestamp?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_tracking_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          base_price: number
          created_at: string | null
          customer_id: string
          delivered_at: string | null
          delivery_address: string
          delivery_contact_name: string
          delivery_contact_phone: string
          delivery_lat: number
          delivery_lng: number
          distance_km: number
          driver_id: string | null
          estimated_delivery_at: string | null
          estimated_duration_minutes: number
          id: string
          package_description: string | null
          package_dimensions: string | null
          package_weight: number | null
          picked_up_at: string | null
          pickup_address: string
          pickup_contact_name: string
          pickup_contact_phone: string
          pickup_lat: number
          pickup_lng: number
          status: Database["public"]["Enums"]["order_status"] | null
          total_price: number
          tracking_code: string
        }
        Insert: {
          base_price: number
          created_at?: string | null
          customer_id: string
          delivered_at?: string | null
          delivery_address: string
          delivery_contact_name: string
          delivery_contact_phone: string
          delivery_lat: number
          delivery_lng: number
          distance_km: number
          driver_id?: string | null
          estimated_delivery_at?: string | null
          estimated_duration_minutes: number
          id?: string
          package_description?: string | null
          package_dimensions?: string | null
          package_weight?: number | null
          picked_up_at?: string | null
          pickup_address: string
          pickup_contact_name: string
          pickup_contact_phone: string
          pickup_lat: number
          pickup_lng: number
          status?: Database["public"]["Enums"]["order_status"] | null
          total_price: number
          tracking_code: string
        }
        Update: {
          base_price?: number
          created_at?: string | null
          customer_id?: string
          delivered_at?: string | null
          delivery_address?: string
          delivery_contact_name?: string
          delivery_contact_phone?: string
          delivery_lat?: number
          delivery_lng?: number
          distance_km?: number
          driver_id?: string | null
          estimated_delivery_at?: string | null
          estimated_duration_minutes?: number
          id?: string
          package_description?: string | null
          package_dimensions?: string | null
          package_weight?: number | null
          picked_up_at?: string | null
          pickup_address?: string
          pickup_contact_name?: string
          pickup_contact_phone?: string
          pickup_lat?: number
          pickup_lng?: number
          status?: Database["public"]["Enums"]["order_status"] | null
          total_price?: number
          tracking_code?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string
          full_name: string
          id: string
          phone: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          full_name: string
          id: string
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          full_name?: string
          id?: string
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      generate_tracking_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      driver_status: "offline" | "available" | "busy" | "on_break"
      order_status:
        | "pending"
        | "confirmed"
        | "picked_up"
        | "in_transit"
        | "delivered"
        | "cancelled"
      user_role: "customer" | "driver" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      driver_status: ["offline", "available", "busy", "on_break"],
      order_status: [
        "pending",
        "confirmed",
        "picked_up",
        "in_transit",
        "delivered",
        "cancelled",
      ],
      user_role: ["customer", "driver", "admin"],
    },
  },
} as const
