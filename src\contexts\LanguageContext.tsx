
import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';

interface LanguageContextType {
  language: 'en' | 'bn';
  setLanguage: (lang: 'en' | 'bn') => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<'en' | 'bn'>('en');

  const translations = useMemo(() => ({
    en: {
      // Common translations
      home: 'Home',
      sendPackage: 'Send Package',
      trackPackage: 'Track Package',
      estimatedDeliveryTime: 'Estimated Delivery Time',
      aboutUs: 'About Us',
      contactUs: 'Contact Us',
      services: 'Our Services',
      testimonials: 'Testimonials',
      faq: 'FAQ',
      enterTrackingId: 'Enter your tracking ID',
      track: 'Track',
      pickupAddress: 'Pickup Address',
      deliveryAddress: 'Delivery Address',
      packageDetails: 'Package Details',
      packageSize: 'Package Size',
      deliverySpeed: 'Delivery Speed',
      packageDescription: 'Package Description',
      senderInformation: 'Sender Information',
      receiverInformation: 'Receiver Information',
      fullName: 'Full Name',
      phoneNumber: 'Phone Number',
      bookDelivery: 'Book Delivery',
      calculatePrice: 'Calculate Price',
      standard: 'Standard',
      express: 'Express',
      urgent: 'Urgent',
      small: 'Small (up to 1kg)',
      medium: 'Medium (1-5kg)',
      large: 'Large (5-15kg)',
      xl: 'Extra Large (15kg+)',
      realTimeUpdates: 'Real-time Updates',
      professionalDrivers: 'Professional Drivers',
      instantQuotes: 'Instant Quotes',
      fastReliableDelivery: 'Fast, reliable delivery across the city',
      howItWorks: 'How it Works',
      step1: 'Request a Pickup',
      step2: 'Package Tracking',
      step3: 'Fast Delivery',
      requestPickupDescription: 'Tell us where to pick up the package.',
      packageTrackingDescription: 'Track your package in real-time.',
      fastDeliveryDescription: 'Get your package delivered quickly.',
      happyCustomers: 'Happy Customers',
      readMore: 'Read More',
      recentPosts: 'Recent Posts',
      allRightsReserved: 'All Rights Reserved',
      company: 'Company',
      legal: 'Legal',
      termsOfService: 'Terms of Service',
      privacyPolicy: 'Privacy Policy',
      india: 'India',
      
      // Hero section translations
      heroTitle: 'Fast & Reliable Hyperlocal Delivery Service',
      heroSubtitle: 'Send packages across your city with real-time tracking, professional drivers, and instant quotes in ₹ (INR)',
      getStarted: 'Get Started',
      readyToSend: 'Ready to Send Your Package?',
      startSendingToday: 'Join thousands of satisfied customers who trust QuickDeliver for their delivery needs.',
      
      // Features translations
      fastDelivery: 'Fast Delivery',
      realtimeTracking: 'Real-time Tracking',
      realtimeTrackingDescription: 'Track your package every step of the way with live GPS updates.',
      professionalDriversDescription: 'Verified drivers with excellent ratings and local knowledge.',
      
      // Testimonials
      testimonial1: 'QuickDeliver saved my business! Same-day delivery across Mumbai at affordable rates.',
      customer1: 'Priya Sharma',
      location1: 'Mumbai, Maharashtra',
      testimonial2: 'Excellent service! My package reached from Kolkata to Howrah in just 2 hours.',
      customer2: 'Rahul Das',
      location2: 'Kolkata, West Bengal',
      testimonial3: 'Professional drivers and real-time tracking. Highly recommended for urgent deliveries.',
      customer3: 'Amit Singh',
      location3: 'Delhi, NCR',
      
      // Auth translations
      welcomeToQuickDeliver: 'Welcome to QuickDeliver',
      signInOrCreateAccount: 'Sign in or create your account',
      signIn: 'Sign In',
      signUp: 'Sign Up',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      phone: 'Phone Number',
      accountType: 'Account Type',
      customer: 'Customer',
      driver: 'Driver',
      enterEmail: 'Enter your email',
      enterPassword: 'Enter your password',
      enterFullName: 'Enter your full name',
      enterPhone: 'Enter your phone number',
      selectAccountType: 'Select account type',
      createAccount: 'Create Account',
      profile: 'Profile',
      myOrders: 'My Orders',
      driverDashboard: 'Driver Dashboard',
      settings: 'Settings',
      signOut: 'Sign Out',
      driverPortal: 'Driver Portal',
    },
    bn: {
      // Common translations
      home: 'হোম',
      sendPackage: 'প্যাকেজ পাঠান',
      trackPackage: 'প্যাকেজ ট্র্যাক করুন',
      estimatedDeliveryTime: 'আনুমানিক ডেলিভারি সময়',
      aboutUs: 'আমাদের সম্পর্কে',
      contactUs: 'যোগাযোগ করুন',
      services: 'আমাদের পরিষেবা',
      testimonials: 'প্রশংসাপত্র',
      faq: 'সাধারণ জিজ্ঞাসা',
      enterTrackingId: 'আপনার ট্র্যাকিং আইডি প্রবেশ করুন',
      track: 'ট্র্যাক',
      pickupAddress: 'পিকআপ ঠিকানা',
      deliveryAddress: 'ডেলিভারি ঠিকানা',
      packageDetails: 'প্যাকেজের বিবরণ',
      packageSize: 'প্যাকেজের আকার',
      deliverySpeed: 'ডেলিভারি গতি',
      packageDescription: 'প্যাকেজের বর্ণনা',
      senderInformation: 'প্রেরকের তথ্য',
      receiverInformation: 'গ্রহীতার তথ্য',
      fullName: 'পুরো নাম',
      phoneNumber: 'ফোন নম্বর',
      bookDelivery: 'ডেলিভারি বুক করুন',
      calculatePrice: 'মূল্য হিসাব করুন',
      standard: 'স্ট্যান্ডার্ড',
      express: 'এক্সপ্রেস',
      urgent: 'জরুরী',
      small: 'ছোট (1 কেজি পর্যন্ত)',
      medium: 'মাঝারি (1-5 কেজি)',
      large: 'বড় (5-15 কেজি)',
      xl: 'অতিরিক্ত বড় (15 কেজি+)',
      realTimeUpdates: 'রিয়েল-টাইম আপডেট',
      professionalDrivers: 'পেশাদার ড্রাইভার',
      instantQuotes: 'তাত্ক্ষণিক উদ্ধৃতি',
      fastReliableDelivery: 'শহরের মধ্যে দ্রুত, নির্ভরযোগ্য ডেলিভারি',
      howItWorks: 'কিভাবে এটা কাজ করে',
      step1: 'পিকআপের জন্য অনুরোধ করুন',
      step2: 'প্যাকেজ ট্র্যাকিং',
      step3: 'দ্রুত ডেলিভারি',
      requestPickupDescription: 'আমাদের বলুন কোথায় প্যাকেজটি নিতে হবে।',
      packageTrackingDescription: 'রিয়েল-টাইমে আপনার প্যাকেজ ট্র্যাক করুন।',
      fastDeliveryDescription: 'দ্রুত আপনার প্যাকেজ বিতরণ করুন।',
      happyCustomers: 'খুশি গ্রাহক',
      readMore: 'আরও পড়ুন',
      recentPosts: 'সাম্প্রতিক পোস্ট',
      allRightsReserved: 'সর্বস্বত্ব সংরক্ষিত',
      company: 'কোম্পানি',
      legal: 'আইনগত',
      termsOfService: 'পরিষেবার শর্তাবলী',
      privacyPolicy: 'গোপনীয়তা নীতি',
      india: 'ভারত',
      
      // Hero section translations
      heroTitle: 'দ্রুত ও নির্ভরযোগ্য হাইপারলোকাল ডেলিভারি সেবা',
      heroSubtitle: 'রিয়েল-টাইম ট্র্যাকিং, পেশাদার ড্রাইভার এবং ₹ (INR) এ তাৎক্ষণিক উদ্ধৃতি সহ আপনার শহর জুড়ে প্যাকেজ পাঠান',
      getStarted: 'শুরু করুন',
      readyToSend: 'আপনার প্যাকেজ পাঠাতে প্রস্তুত?',
      startSendingToday: 'হাজার হাজার সন্তুষ্ট গ্রাহকদের সাথে যোগ দিন যারা তাদের ডেলিভারি প্রয়োজনের জন্য QuickDeliver-কে বিশ্বাস করেন।',
      
      // Features translations
      fastDelivery: 'দ্রুত ডেলিভারি',
      realtimeTracking: 'রিয়েল-টাইম ট্র্যাকিং',
      realtimeTrackingDescription: 'লাইভ GPS আপডেটের সাথে প্রতিটি ধাপে আপনার প্যাকেজ ট্র্যাক করুন।',
      professionalDriversDescription: 'চমৎকার রেটিং এবং স্থানীয় জ্ঞান সহ যাচাইকৃত ড্রাইভার।',
      
      // Testimonials
      testimonial1: 'QuickDeliver আমার ব্যবসা বাঁচিয়েছে! সাশ্রয়ী দামে মুম্বাই জুড়ে একই দিনে ডেলিভারি।',
      customer1: 'প্রিয়া শর্মা',
      location1: 'মুম্বাই, মহারাষ্ট্র',
      testimonial2: 'চমৎকার সেবা! আমার প্যাকেজ মাত্র ২ ঘন্টায় কলকাতা থেকে হাওড়ায় পৌঁছেছে।',
      customer2: 'রাহুল দাস',
      location2: 'কলকাতা, পশ্চিমবঙ্গ',
      testimonial3: 'পেশাদার ড্রাইভার এবং রিয়েল-টাইম ট্র্যাকিং। জরুরি ডেলিভারির জন্য অত্যন্ত সুপারিশকৃত।',
      customer3: 'অমিত সিং',
      location3: 'দিল্লি, NCR',
      
      // Auth translations
      welcomeToQuickDeliver: 'QuickDeliver-এ স্বাগতম',
      signInOrCreateAccount: 'সাইন ইন করুন বা আপনার অ্যাকাউন্ট তৈরি করুন',
      signIn: 'সাইন ইন',
      signUp: 'সাইন আপ',
      email: 'ইমেইল',
      password: 'পাসওয়ার্ড',
      confirmPassword: 'পাসওয়ার্ড নিশ্চিত করুন',
      phone: 'ফোন নম্বর',
      accountType: 'অ্যাকাউন্টের ধরন',
      customer: 'গ্রাহক',
      driver: 'চালক',
      enterEmail: 'আপনার ইমেইল লিখুন',
      enterPassword: 'আপনার পাসওয়ার্ড লিখুন',
      enterFullName: 'আপনার পূর্ণ নাম লিখুন',
      enterPhone: 'আপনার ফোন নম্বর লিখুন',
      selectAccountType: 'অ্যাকাউন্টের ধরন নির্বাচন করুন',
      createAccount: 'অ্যাকাউন্ট তৈরি করুন',
      profile: 'প্রোফাইল',
      myOrders: 'আমার অর্ডার',
      driverDashboard: 'চালক ড্যাশবোর্ড',
      settings: 'সেটিংস',
      signOut: 'সাইন আউট',
      driverPortal: 'চালক পোর্টাল',
    }
  }), []);

  const t = useCallback((key: string) => {
    return translations[language][key] || key;
  }, [language, translations]);

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
