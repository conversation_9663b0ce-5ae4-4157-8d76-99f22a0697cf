import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Package, MapPin, Clock, Calculator, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import UserAuthModal from "@/components/auth/UserAuthModal";
import { supabase } from "@/integrations/supabase/client";

const SendPackage = () => {
  const { user } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  
  const [formData, setFormData] = useState({
    pickupAddress: "",
    deliveryAddress: "",
    packageSize: "",
    urgency: "",
    description: "",
    senderName: "",
    senderPhone: "",
    receiverName: "",
    receiverPhone: ""
  });

  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<string | null>(null);

  const calculateQuote = () => {
    // Simple pricing calculation based on package size and urgency
    let basePrice = 5;
    
    switch (formData.packageSize) {
      case "small": basePrice += 0; break;
      case "medium": basePrice += 3; break;
      case "large": basePrice += 7; break;
      case "xl": basePrice += 12; break;
    }

    switch (formData.urgency) {
      case "standard": basePrice += 0; setEstimatedTime("2-4 hours"); break;
      case "express": basePrice += 5; setEstimatedTime("45-90 minutes"); break;
      case "urgent": basePrice += 12; setEstimatedTime("20-45 minutes"); break;
    }

    setEstimatedPrice(basePrice);
    toast.success("Quote calculated successfully!");
  };

  const generateTrackingCode = () => {
    const prefix = "QD";
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setAuthModalOpen(true);
      return;
    }

    if (!estimatedPrice) {
      toast.error("Please calculate the quote first");
      return;
    }

    try {
      const trackingCode = generateTrackingCode();

      // Create order in database
      const { data, error } = await supabase
        .from('orders')
        .insert({
          customer_id: user.id,
          pickup_address: formData.pickupAddress,
          delivery_address: formData.deliveryAddress,
          pickup_contact_name: formData.senderName,
          pickup_contact_phone: formData.senderPhone,
          delivery_contact_name: formData.receiverName,
          delivery_contact_phone: formData.receiverPhone,
          package_description: formData.description,
          package_dimensions: formData.packageSize,
          base_price: estimatedPrice,
          total_price: estimatedPrice,
          tracking_code: trackingCode,
          status: 'pending',
          pickup_lat: 0, // In real app, get from geocoding
          pickup_lng: 0,
          delivery_lat: 0,
          delivery_lng: 0,
          distance_km: 5, // Mock distance
          estimated_duration_minutes: formData.urgency === 'urgent' ? 30 : formData.urgency === 'express' ? 60 : 180
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating order:', error);
        toast.error("Failed to create order. Please try again.");
        return;
      }

      toast.success(`Package booking successful! Your tracking ID is: ${trackingCode}`);

      // Reset form
      setFormData({
        pickupAddress: "",
        deliveryAddress: "",
        packageSize: "",
        urgency: "",
        description: "",
        senderName: "",
        senderPhone: "",
        receiverName: "",
        receiverPhone: ""
      });
      setEstimatedPrice(null);
      setEstimatedTime(null);

    } catch (error) {
      console.error('Error submitting order:', error);
      toast.error("Failed to submit order. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      {/* Remove the existing header section since we have global Header */}

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <Package className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Send a Package</h1>
            <p className="text-gray-600">Fast, reliable delivery across the city</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>Package Details</span>
                  </CardTitle>
                  <CardDescription>
                    Enter pickup and delivery information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Pickup & Delivery */}
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="pickup">Pickup Address</Label>
                        <Input
                          id="pickup"
                          placeholder="Enter pickup address"
                          value={formData.pickupAddress}
                          onChange={(e) => setFormData({...formData, pickupAddress: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="delivery">Delivery Address</Label>
                        <Input
                          id="delivery"
                          placeholder="Enter delivery address"
                          value={formData.deliveryAddress}
                          onChange={(e) => setFormData({...formData, deliveryAddress: e.target.value})}
                          required
                        />
                      </div>
                    </div>

                    {/* Package Info */}
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="size">Package Size</Label>
                        <Select value={formData.packageSize} onValueChange={(value) => setFormData({...formData, packageSize: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select package size" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="small">Small (up to 1kg)</SelectItem>
                            <SelectItem value="medium">Medium (1-5kg)</SelectItem>
                            <SelectItem value="large">Large (5-15kg)</SelectItem>
                            <SelectItem value="xl">Extra Large (15kg+)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="urgency">Delivery Speed</Label>
                        <Select value={formData.urgency} onValueChange={(value) => setFormData({...formData, urgency: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select delivery speed" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="standard">Standard (2-4 hours)</SelectItem>
                            <SelectItem value="express">Express (45-90 minutes)</SelectItem>
                            <SelectItem value="urgent">Urgent (20-45 minutes)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                      <Label htmlFor="description">Package Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Brief description of the package contents"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                      />
                    </div>

                    {/* Contact Info */}
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="font-semibold text-gray-900">Sender Information</h3>
                        <div className="space-y-2">
                          <Label htmlFor="senderName">Full Name</Label>
                          <Input
                            id="senderName"
                            placeholder="Sender name"
                            value={formData.senderName}
                            onChange={(e) => setFormData({...formData, senderName: e.target.value})}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="senderPhone">Phone Number</Label>
                          <Input
                            id="senderPhone"
                            placeholder="Sender phone"
                            value={formData.senderPhone}
                            onChange={(e) => setFormData({...formData, senderPhone: e.target.value})}
                            required
                          />
                        </div>
                      </div>
                      <div className="space-y-4">
                        <h3 className="font-semibold text-gray-900">Receiver Information</h3>
                        <div className="space-y-2">
                          <Label htmlFor="receiverName">Full Name</Label>
                          <Input
                            id="receiverName"
                            placeholder="Receiver name"
                            value={formData.receiverName}
                            onChange={(e) => setFormData({...formData, receiverName: e.target.value})}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="receiverPhone">Phone Number</Label>
                          <Input
                            id="receiverPhone"
                            placeholder="Receiver phone"
                            value={formData.receiverPhone}
                            onChange={(e) => setFormData({...formData, receiverPhone: e.target.value})}
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700">
                      Book Delivery
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Quote Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calculator className="h-5 w-5" />
                    <span>Get Quote</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={calculateQuote} 
                    className="w-full mb-4"
                    disabled={!formData.packageSize || !formData.urgency}
                  >
                    Calculate Price
                  </Button>
                  
                  {estimatedPrice && (
                    <div className="space-y-3 p-4 bg-blue-50 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Estimated Price:</span>
                        <span className="text-2xl font-bold text-blue-600">${estimatedPrice}</span>
                      </div>
                      {estimatedTime && (
                        <div className="flex justify-between items-center">
                          <span className="font-medium">Delivery Time:</span>
                          <span className="text-green-600 font-medium">{estimatedTime}</span>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>Delivery Times</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Standard</span>
                    <span className="font-medium">2-4 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Express</span>
                    <span className="font-medium">45-90 min</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Urgent</span>
                    <span className="font-medium">20-45 min</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <UserAuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultTab="signup"
      />
    </div>
  );
};

export default SendPackage;
