
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Truck, 
  Package, 
  MapPin, 
  Clock, 
  DollarSign, 
  Star, 
  Navigation,
  Phone,
  CheckCircle,
  ArrowLeft,
  BarChart3
} from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from "sonner";

const DriverDashboard = () => {
  const [driverStatus, setDriverStatus] = useState<'online' | 'offline'>('offline');
  
  // Mock driver data
  const driverStats = {
    todayEarnings: 127.50,
    completedDeliveries: 8,
    rating: 4.9,
    activeDeliveries: 2
  };

  const availableJobs = [
    {
      id: "QD001",
      pickup: "123 Main St",
      delivery: "456 Oak Ave",
      distance: "2.3 km",
      payment: "$12.50",
      urgency: "standard",
      packageSize: "medium"
    },
    {
      id: "QD002", 
      pickup: "789 Pine Rd",
      delivery: "321 Elm St",
      distance: "1.8 km",
      payment: "$18.00",
      urgency: "express",
      packageSize: "small"
    },
    {
      id: "QD003",
      pickup: "555 Cedar Ln",
      delivery: "777 Birch Ave",
      distance: "4.1 km",
      payment: "$25.00",
      urgency: "urgent",
      packageSize: "large"
    }
  ];

  const activeDeliveries = [
    {
      id: "QD004",
      pickup: "Downtown Mall",
      delivery: "Uptown Plaza",
      status: "picked_up",
      eta: "15 minutes",
      customer: "John Doe",
      phone: "+****************"
    },
    {
      id: "QD005",
      pickup: "City Center",
      delivery: "Riverside District", 
      status: "en_route",
      eta: "25 minutes",
      customer: "Jane Smith",
      phone: "+****************"
    }
  ];

  const toggleStatus = () => {
    const newStatus = driverStatus === 'online' ? 'offline' : 'online';
    setDriverStatus(newStatus);
    toast.success(`You are now ${newStatus}`);
  };

  const acceptJob = (jobId: string) => {
    toast.success(`Job ${jobId} accepted! Navigate to pickup location.`);
  };

  const completeDelivery = (deliveryId: string) => {
    toast.success(`Delivery ${deliveryId} marked as completed!`);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'express': return 'bg-orange-100 text-orange-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'picked_up': return 'bg-blue-100 text-blue-800';
      case 'en_route': return 'bg-orange-100 text-orange-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors">
            <ArrowLeft className="h-5 w-5" />
            <span className="font-medium">Back to Home</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Badge variant={driverStatus === 'online' ? 'default' : 'secondary'}>
              {driverStatus === 'online' ? 'Online' : 'Offline'}
            </Badge>
            <Button 
              onClick={toggleStatus}
              variant={driverStatus === 'online' ? 'destructive' : 'default'}
              className={driverStatus === 'online' ? '' : 'bg-green-600 hover:bg-green-700'}
            >
              {driverStatus === 'online' ? 'Go Offline' : 'Go Online'}
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <Truck className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Driver Dashboard</h1>
            <p className="text-gray-600">Manage your deliveries and track earnings</p>
          </div>

          {/* Stats Cards */}
          <div className="grid md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Today's Earnings</p>
                    <p className="text-2xl font-bold text-green-600">${driverStats.todayEarnings}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-blue-600">{driverStats.completedDeliveries}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Rating</p>
                    <p className="text-2xl font-bold text-orange-500">{driverStats.rating}</p>
                  </div>
                  <Star className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active</p>
                    <p className="text-2xl font-bold text-purple-600">{driverStats.activeDeliveries}</p>
                  </div>
                  <Package className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="available" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="available">Available Jobs</TabsTrigger>
              <TabsTrigger value="active">Active Deliveries</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            {/* Available Jobs */}
            <TabsContent value="available">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Package className="h-5 w-5" />
                    <span>Available Jobs</span>
                  </CardTitle>
                  <CardDescription>
                    Accept new delivery jobs in your area
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {driverStatus === 'offline' ? (
                    <div className="text-center py-8">
                      <Truck className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">You're Offline</h3>
                      <p className="text-gray-500 mb-4">Go online to see available delivery jobs</p>
                      <Button onClick={toggleStatus} className="bg-green-600 hover:bg-green-700">
                        Go Online
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {availableJobs.map((job) => (
                        <Card key={job.id} className="border border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <Badge variant="outline">{job.id}</Badge>
                                  <Badge className={getUrgencyColor(job.urgency)}>
                                    {job.urgency}
                                  </Badge>
                                  <Badge variant="secondary">{job.packageSize}</Badge>
                                </div>
                                <div className="space-y-1">
                                  <div className="flex items-center space-x-2 text-sm">
                                    <MapPin className="h-4 w-4 text-green-600" />
                                    <span className="font-medium">Pickup:</span>
                                    <span>{job.pickup}</span>
                                  </div>
                                  <div className="flex items-center space-x-2 text-sm">
                                    <MapPin className="h-4 w-4 text-blue-600" />
                                    <span className="font-medium">Delivery:</span>
                                    <span>{job.delivery}</span>
                                  </div>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                                    <span>{job.distance}</span>
                                    <span className="font-bold text-green-600">{job.payment}</span>
                                  </div>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline">
                                  <Navigation className="h-4 w-4 mr-1" />
                                  Route
                                </Button>
                                <Button size="sm" onClick={() => acceptJob(job.id)}>
                                  Accept
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Active Deliveries */}
            <TabsContent value="active">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Truck className="h-5 w-5" />
                    <span>Active Deliveries</span>
                  </CardTitle>
                  <CardDescription>
                    Manage your current deliveries
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {activeDeliveries.length === 0 ? (
                    <div className="text-center py-8">
                      <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">No Active Deliveries</h3>
                      <p className="text-gray-500">Accept jobs from the Available Jobs tab</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {activeDeliveries.map((delivery) => (
                        <Card key={delivery.id} className="border border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <Badge variant="outline">{delivery.id}</Badge>
                                  <Badge className={getStatusColor(delivery.status)}>
                                    {delivery.status.replace('_', ' ')}
                                  </Badge>
                                </div>
                                <div className="space-y-1">
                                  <div className="flex items-center space-x-2 text-sm">
                                    <MapPin className="h-4 w-4 text-green-600" />
                                    <span className="font-medium">From:</span>
                                    <span>{delivery.pickup}</span>
                                  </div>
                                  <div className="flex items-center space-x-2 text-sm">
                                    <MapPin className="h-4 w-4 text-blue-600" />
                                    <span className="font-medium">To:</span>
                                    <span>{delivery.delivery}</span>
                                  </div>
                                  <div className="flex items-center space-x-2 text-sm">
                                    <Clock className="h-4 w-4 text-orange-600" />
                                    <span className="font-medium">ETA:</span>
                                    <span>{delivery.eta}</span>
                                  </div>
                                  <div className="flex items-center space-x-2 text-sm">
                                    <span className="font-medium">Customer:</span>
                                    <span>{delivery.customer}</span>
                                  </div>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline">
                                  <Phone className="h-4 w-4 mr-1" />
                                  Call
                                </Button>
                                <Button size="sm" variant="outline">
                                  <Navigation className="h-4 w-4 mr-1" />
                                  Navigate
                                </Button>
                                <Button size="sm" onClick={() => completeDelivery(delivery.id)}>
                                  Complete
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Analytics */}
            <TabsContent value="analytics">
              <div className="grid lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="h-5 w-5" />
                      <span>Weekly Earnings</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Earnings chart would appear here</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Completion Rate</span>
                      <span className="font-bold text-green-600">98%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Average Rating</span>
                      <span className="font-bold text-orange-500">4.9</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>On-time Delivery</span>
                      <span className="font-bold text-blue-600">95%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Total Deliveries</span>
                      <span className="font-bold text-purple-600">247</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default DriverDashboard;
