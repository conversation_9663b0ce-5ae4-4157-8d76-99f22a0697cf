import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Truck, MapPin, Star, Package, Navigation, Phone, AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import DriverAuthModal from "@/components/auth/DriverAuthModal";

interface Order {
  id: string;
  tracking_code: string;
  pickup_address: string;
  delivery_address: string;
  pickup_contact_name: string;
  pickup_contact_phone: string;
  delivery_contact_name: string;
  delivery_contact_phone: string;
  total_price: number;
  status: string;
  distance_km: number;
}

const DriverDashboard = () => {
  const { user, profile } = useAuth();
  const [driverStatus, setDriverStatus] = useState<'offline' | 'available' | 'busy'>('offline');
  const [availableOrders, setAvailableOrders] = useState<Order[]>([]);
  const [activeOrder, setActiveOrder] = useState<Order | null>(null);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setAuthModalOpen(true);
      setLoading(false);
      return;
    }

    if (profile?.role !== 'driver') {
      toast.error("Access denied. Driver account required.");
      setLoading(false);
      return;
    }

    fetchOrders();
    setLoading(false);
  }, [user, profile]);

  const fetchOrders = async () => {
    try {
      const { data: available, error: availableError } = await supabase
        .from('orders')
        .select('*')
        .is('driver_id', null)
        .in('status', ['pending', 'confirmed'])
        .order('created_at', { ascending: true });

      if (availableError) {
        console.error('Error fetching available orders:', availableError);
      } else {
        setAvailableOrders(available || []);
      }

      const { data: active, error: activeError } = await supabase
        .from('orders')
        .select('*')
        .eq('driver_id', user?.id)
        .in('status', ['picked_up', 'in_transit'])
        .single();

      if (activeError && activeError.code !== 'PGRST116') {
        console.error('Error fetching active order:', activeError);
      } else if (active) {
        setActiveOrder(active);
        setDriverStatus('busy');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    }
  };

  const toggleStatus = async () => {
    if (!user) return;
    const newStatus = driverStatus === 'offline' ? 'available' : 'offline';
    setDriverStatus(newStatus);
    toast.success(`Status updated to ${newStatus}`);
  };

  const acceptOrder = async (orderId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('orders')
        .update({
          driver_id: user.id,
          status: 'confirmed'
        })
        .eq('id', orderId);

      if (error) {
        toast.error("Failed to accept order");
      } else {
        toast.success("Order accepted!");
        fetchOrders();
        setDriverStatus('busy');
      }
    } catch (error) {
      console.error('Error accepting order:', error);
    }
  };

  const markPickedUp = async (orderId: string) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({
          status: 'picked_up',
          picked_up_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) {
        toast.error("Failed to update status");
      } else {
        toast.success("Package marked as picked up!");
        fetchOrders();
      }
    } catch (error) {
      console.error('Error marking picked up:', error);
    }
  };

  const markDelivered = async (orderId: string) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({
          status: 'delivered',
          delivered_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) {
        toast.error("Failed to update status");
      } else {
        toast.success("Package marked as delivered!");
        setActiveOrder(null);
        setDriverStatus('available');
        fetchOrders();
      }
    } catch (error) {
      console.error('Error marking delivered:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Truck className="h-12 w-12 text-orange-600 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Loading driver dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user || profile?.role !== 'driver') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-orange-600 mx-auto mb-4" />
            <CardTitle>Driver Access Required</CardTitle>
            <CardDescription>
              Please sign in with a driver account to access this dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setAuthModalOpen(true)}
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              Driver Login
            </Button>
          </CardContent>
        </Card>

        <DriverAuthModal
          isOpen={authModalOpen}
          onClose={() => setAuthModalOpen(false)}
          defaultTab="signin"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <Truck className="h-12 w-12 text-orange-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Driver Dashboard</h1>
            <p className="text-gray-600">Manage your deliveries and earnings</p>
          </div>

          <div className="grid md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <Badge
                    variant={driverStatus === 'available' ? 'default' : driverStatus === 'busy' ? 'destructive' : 'secondary'}
                    className="capitalize"
                  >
                    {driverStatus}
                  </Badge>
                  <Button
                    size="sm"
                    onClick={toggleStatus}
                    variant={driverStatus === 'offline' ? 'default' : 'outline'}
                  >
                    {driverStatus === 'offline' ? 'Go Online' : 'Go Offline'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Today's Earnings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">₹450</div>
                <p className="text-sm text-gray-600">+12% from yesterday</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Deliveries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-sm text-gray-600">Completed today</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Rating</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-2xl font-bold">4.8</span>
                </div>
                <p className="text-sm text-gray-600">Based on 156 reviews</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Available Orders ({availableOrders.length})</span>
                </CardTitle>
                <CardDescription>
                  Orders waiting for pickup in your area
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {availableOrders.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No available orders</p>
                ) : (
                  availableOrders.map((order) => (
                    <div key={order.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold">Order {order.tracking_code}</h3>
                          <p className="text-sm text-gray-600">{order.distance_km} km • ₹{order.total_price}</p>
                        </div>
                        <Button size="sm" onClick={() => acceptOrder(order.id)}>Accept</Button>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-sm">
                          <MapPin className="h-4 w-4 text-green-600" />
                          <span>Pickup: {order.pickup_address}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <MapPin className="h-4 w-4 text-blue-600" />
                          <span>Delivery: {order.delivery_address}</span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Navigation className="h-5 w-5" />
                  <span>Active Delivery</span>
                </CardTitle>
                <CardDescription>
                  Current delivery in progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!activeOrder ? (
                  <p className="text-gray-500 text-center py-4">No active deliveries</p>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">Order {activeOrder.tracking_code}</h3>
                        <p className="text-sm text-gray-600">{activeOrder.distance_km} km • ₹{activeOrder.total_price}</p>
                      </div>
                      <Badge variant="default">In Progress</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <MapPin className="h-4 w-4 text-green-600" />
                        <span>Pickup: {activeOrder.pickup_address}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <MapPin className="h-4 w-4 text-blue-600" />
                        <span>Delivery: {activeOrder.delivery_address}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {activeOrder.status === 'confirmed' && (
                        <Button size="sm" onClick={() => markPickedUp(activeOrder.id)} className="flex-1">
                          Mark Picked Up
                        </Button>
                      )}
                      {activeOrder.status === 'picked_up' && (
                        <Button size="sm" onClick={() => markDelivered(activeOrder.id)} className="flex-1">
                          Mark Delivered
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="flex-1">
                        <Phone className="h-4 w-4 mr-2" />
                        Call Customer
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverDashboard;