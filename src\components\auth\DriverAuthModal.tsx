import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Loader2, Mail, Lock, User, Phone, Truck, Car, Bike } from 'lucide-react';
import { toast } from 'sonner';

interface DriverAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'signin' | 'signup';
}

const DriverAuthModal: React.FC<DriverAuthModalProps> = ({ isOpen, onClose, defaultTab = 'signin' }) => {
  const { signIn, signUp } = useAuth();
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState<'signin' | 'signup'>(defaultTab);
  const [formLoading, setFormLoading] = useState(false);

  const [signInData, setSignInData] = useState({
    email: '',
    password: '',
  });

  const [signUpData, setSignUpData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    vehicleType: '',
    vehicleNumber: '',
    licenseNumber: '',
  });

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!signInData.email || !signInData.password) {
      toast.error('Please fill in all fields');
      return;
    }

    setFormLoading(true);
    try {
      await signIn(signInData.email, signInData.password);
      onClose();
      setSignInData({ email: '', password: '' });
    } catch (error) {
      console.error('Sign in error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (signUpData.password !== signUpData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (signUpData.password.length < 6) {
      toast.error('Password must be at least 6 characters');
      return;
    }

    if (!signUpData.email || !signUpData.password || !signUpData.fullName || 
        !signUpData.vehicleType || !signUpData.vehicleNumber || !signUpData.licenseNumber) {
      toast.error('Please fill in all required fields');
      return;
    }

    setFormLoading(true);
    try {
      await signUp(
        signUpData.email,
        signUpData.password,
        signUpData.fullName,
        signUpData.phone,
        'driver'
      );
      onClose();
      setSignUpData({
        email: '',
        password: '',
        confirmPassword: '',
        fullName: '',
        phone: '',
        vehicleType: '',
        vehicleNumber: '',
        licenseNumber: '',
      });
    } catch (error) {
      console.error('Sign up error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const vehicleIcons = {
    car: Car,
    bike: Bike,
    truck: Truck,
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <Truck className="w-6 h-6 text-orange-600" />
            Driver Portal
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600">
            Join our driver network and start earning
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'signin' | 'signup')} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="signin">{t('signIn')}</TabsTrigger>
            <TabsTrigger value="signup">Join as Driver</TabsTrigger>
          </TabsList>

          <TabsContent value="signin" className="space-y-4">
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="driver-signin-email">{t('email')}</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signin-email"
                    type="email"
                    placeholder={t('enterEmail')}
                    value={signInData.email}
                    onChange={(e) => setSignInData({ ...signInData, email: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="driver-signin-password">{t('password')}</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signin-password"
                    type="password"
                    placeholder={t('enterPassword')}
                    value={signInData.password}
                    onChange={(e) => setSignInData({ ...signInData, password: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={formLoading}>
                {formLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {t('signIn')}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="driver-signup-name">{t('fullName')}</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signup-name"
                    type="text"
                    placeholder={t('enterFullName')}
                    value={signUpData.fullName}
                    onChange={(e) => setSignUpData({ ...signUpData, fullName: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="driver-signup-email">{t('email')}</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signup-email"
                    type="email"
                    placeholder={t('enterEmail')}
                    value={signUpData.email}
                    onChange={(e) => setSignUpData({ ...signUpData, email: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="driver-signup-phone">{t('phone')} *</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signup-phone"
                    type="tel"
                    placeholder={t('enterPhone')}
                    value={signUpData.phone}
                    onChange={(e) => setSignUpData({ ...signUpData, phone: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="vehicle-type">Vehicle Type *</Label>
                <Select 
                  value={signUpData.vehicleType} 
                  onValueChange={(value) => setSignUpData({ ...signUpData, vehicleType: value })}
                  disabled={formLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select vehicle type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bike">
                      <div className="flex items-center space-x-2">
                        <Bike className="h-4 w-4" />
                        <span>Motorcycle/Bike</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="car">
                      <div className="flex items-center space-x-2">
                        <Car className="h-4 w-4" />
                        <span>Car</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="truck">
                      <div className="flex items-center space-x-2">
                        <Truck className="h-4 w-4" />
                        <span>Truck/Van</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="vehicle-number">Vehicle Number *</Label>
                <Input
                  id="vehicle-number"
                  type="text"
                  placeholder="e.g., MH 01 AB 1234"
                  value={signUpData.vehicleNumber}
                  onChange={(e) => setSignUpData({ ...signUpData, vehicleNumber: e.target.value })}
                  required
                  disabled={formLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="license-number">License Number *</Label>
                <Input
                  id="license-number"
                  type="text"
                  placeholder="Driving license number"
                  value={signUpData.licenseNumber}
                  onChange={(e) => setSignUpData({ ...signUpData, licenseNumber: e.target.value })}
                  required
                  disabled={formLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="driver-signup-password">{t('password')}</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signup-password"
                    type="password"
                    placeholder={t('enterPassword')}
                    value={signUpData.password}
                    onChange={(e) => setSignUpData({ ...signUpData, password: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="driver-signup-confirm">{t('confirmPassword')}</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="driver-signup-confirm"
                    type="password"
                    placeholder={t('confirmPassword')}
                    value={signUpData.confirmPassword}
                    onChange={(e) => setSignUpData({ ...signUpData, confirmPassword: e.target.value })}
                    className="pl-10"
                    required
                    disabled={formLoading}
                  />
                </div>
              </div>

              <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={formLoading}>
                {formLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Join as Driver
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default DriverAuthModal;
