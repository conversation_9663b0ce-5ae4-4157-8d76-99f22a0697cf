import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Loader2, Mail, Lock, Shield, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface AdminAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdminAuthModal: React.FC<AdminAuthModalProps> = ({ isOpen, onClose }) => {
  const { signIn } = useAuth();
  const { t } = useLanguage();
  const [formLoading, setFormLoading] = useState(false);

  const [signInData, setSignInData] = useState({
    email: '',
    password: '',
  });

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!signInData.email || !signInData.password) {
      toast.error('Please fill in all fields');
      return;
    }

    // Check if it's an admin email (basic validation)
    if (!signInData.email.includes('admin') && !signInData.email.includes('quickdeliver')) {
      toast.error('Invalid admin credentials');
      return;
    }

    setFormLoading(true);
    try {
      await signIn(signInData.email, signInData.password);
      onClose();
      setSignInData({ email: '', password: '' });
    } catch (error) {
      console.error('Admin sign in error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <Shield className="w-6 h-6 text-red-600" />
            Admin Portal
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600">
            Restricted access for administrators only
          </DialogDescription>
        </DialogHeader>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-sm font-medium">Admin Access Only</span>
          </div>
          <p className="text-sm text-red-700 mt-1">
            This portal is restricted to authorized administrators. Unauthorized access is prohibited.
          </p>
        </div>

        <form onSubmit={handleSignIn} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="admin-signin-email">Admin Email</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="admin-signin-email"
                type="email"
                placeholder="<EMAIL>"
                value={signInData.email}
                onChange={(e) => setSignInData({ ...signInData, email: e.target.value })}
                className="pl-10"
                required
                disabled={formLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="admin-signin-password">Admin Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="admin-signin-password"
                type="password"
                placeholder="Enter admin password"
                value={signInData.password}
                onChange={(e) => setSignInData({ ...signInData, password: e.target.value })}
                className="pl-10"
                required
                disabled={formLoading}
              />
            </div>
          </div>

          <Button type="submit" className="w-full bg-red-600 hover:bg-red-700" disabled={formLoading}>
            {formLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Access Admin Panel
          </Button>
        </form>

        <div className="text-center text-sm text-gray-500 mt-4">
          <p>Need admin access? Contact system administrator.</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdminAuthModal;
