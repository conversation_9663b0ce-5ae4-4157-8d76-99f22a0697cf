// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nlrwttlxhetlymqmotwi.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5scnd0dGx4aGV0bHltcW1vdHdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyNzE0NTEsImV4cCI6MjA2NDg0NzQ1MX0.r3wtjRDs1cPwxf6wefcb4JBc-ffEZN9_54vZ_r2KtSg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);