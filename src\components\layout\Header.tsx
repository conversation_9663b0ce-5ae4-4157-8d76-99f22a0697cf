
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import UserMenu from '@/components/auth/UserMenu';
import UserAuthModal from '@/components/auth/UserAuthModal';
import DriverAuthModal from '@/components/auth/DriverAuthModal';
import AdminAuthModal from '@/components/auth/AdminAuthModal';
import { Package, LogIn, UserPlus, ChevronDown, User, Truck, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  const { user, loading } = useAuth();
  const { t } = useLanguage();
  const [userAuthModalOpen, setUserAuthModalOpen] = useState(false);
  const [driverAuthModalOpen, setDriverAuthModalOpen] = useState(false);
  const [adminAuthModalOpen, setAdminAuthModalOpen] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'signin' | 'signup'>('signin');

  const openUserSignIn = () => {
    setAuthModalTab('signin');
    setUserAuthModalOpen(true);
  };

  const openUserSignUp = () => {
    setAuthModalTab('signup');
    setUserAuthModalOpen(true);
  };

  const openDriverAuth = () => {
    setDriverAuthModalOpen(true);
  };

  const openAdminAuth = () => {
    setAdminAuthModalOpen(true);
  };

  return (
    <>
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-blue-600 to-orange-500 p-2 rounded-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">QuickDeliver</span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link to="/send" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('sendPackage')}
              </Link>
              <Link to="/track" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('trackPackage')}
              </Link>
              <Link to="/driver" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('driverPortal')}
              </Link>
            </nav>

            {/* Auth & Language */}
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              
              {loading ? (
                <div className="flex space-x-2">
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ) : user ? (
                <UserMenu />
              ) : (
                <div className="flex items-center space-x-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="flex items-center space-x-2">
                        <LogIn className="h-4 w-4" />
                        <span className="hidden sm:inline">{t('signIn')}</span>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={openUserSignIn} className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <span>Customer Login</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={openDriverAuth} className="flex items-center space-x-2">
                        <Truck className="h-4 w-4 text-orange-600" />
                        <span>Driver Login</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={openAdminAuth} className="flex items-center space-x-2">
                        <Shield className="h-4 w-4 text-red-600" />
                        <span>Admin Login</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2">
                        <UserPlus className="h-4 w-4" />
                        <span className="hidden sm:inline">{t('signUp')}</span>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={openUserSignUp} className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <span>Join as Customer</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => { setAuthModalTab('signup'); openDriverAuth(); }} className="flex items-center space-x-2">
                        <Truck className="h-4 w-4 text-orange-600" />
                        <span>Join as Driver</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <UserAuthModal
        isOpen={userAuthModalOpen}
        onClose={() => setUserAuthModalOpen(false)}
        defaultTab={authModalTab}
      />

      <DriverAuthModal
        isOpen={driverAuthModalOpen}
        onClose={() => setDriverAuthModalOpen(false)}
        defaultTab={authModalTab}
      />

      <AdminAuthModal
        isOpen={adminAuthModalOpen}
        onClose={() => setAdminAuthModalOpen(false)}
      />
    </>
  );
};

export default Header;
