
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import UserMenu from '@/components/auth/UserMenu';
import AuthModal from '@/components/auth/AuthModal';
import { Package, LogIn, UserPlus } from 'lucide-react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  const { user, loading } = useAuth();
  const { t } = useLanguage();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'signin' | 'signup'>('signin');

  const openSignIn = () => {
    setAuthModalTab('signin');
    setAuthModalOpen(true);
  };

  const openSignUp = () => {
    setAuthModalTab('signup');
    setAuthModalOpen(true);
  };

  return (
    <>
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-blue-600 to-orange-500 p-2 rounded-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">QuickDeliver</span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link to="/send" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('sendPackage')}
              </Link>
              <Link to="/track" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('trackPackage')}
              </Link>
              <Link to="/driver" className="text-gray-600 hover:text-blue-600 transition-colors">
                {t('driverPortal')}
              </Link>
            </nav>

            {/* Auth & Language */}
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              
              {loading ? (
                <div className="flex space-x-2">
                  <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ) : user ? (
                <UserMenu />
              ) : (
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" onClick={openSignIn} className="flex items-center space-x-2">
                    <LogIn className="h-4 w-4" />
                    <span className="hidden sm:inline">{t('signIn')}</span>
                  </Button>
                  <Button onClick={openSignUp} className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2">
                    <UserPlus className="h-4 w-4" />
                    <span className="hidden sm:inline">{t('signUp')}</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <AuthModal 
        isOpen={authModalOpen} 
        onClose={() => setAuthModalOpen(false)}
        defaultTab={authModalTab}
      />
    </>
  );
};

export default Header;
